import type { ActionFunctionArgs, MetaFunction } from "@remix-run/node";
import { Form, useLoaderData } from "@remix-run/react";
import { useState, useEffect, useRef } from "react";

import { streamText, tool } from 'ai';
import { openai } from '~/services/openai.server';
import { tools, toolMap } from '~/tools';
import { getAvailableModels } from '~/services/models.server';

export const meta: MetaFunction = () => {
  return [
    { title: "Chat Bot" },
    { name: "description", content: "A smart chat bot!" },
  ];
};

export async function loader() {
  const models = await getAvailableModels();
  return Response.json({ models });
}

export async function action({ request }: ActionFunctionArgs) {
  const { messages, model } = await request.json();

  const stream = new ReadableStream({
    async start(controller) {
      const encoder = new TextEncoder();
      const sendJSON = (data: object) => {
        controller.enqueue(encoder.encode(`data: ${JSON.stringify(data)}

`));
      };

      try {
        const result = await streamText({
          model: openai(model || 'gpt-4o'),
          messages,
          tools: tools.reduce((acc, t) => {
            acc[t.name] = tool({
              description: t.description,
              inputSchema: t.schema,
              execute: t.execute,
            });
            return acc;
          }, {} as any),
        });

        for await (const part of result.fullStream) {
          if (part.type === 'tool-call') {
            sendJSON({ type: 'tool_call', name: part.toolName, args: part.input });

            const tool = toolMap.get(part.toolName);
            if (!tool) {
              throw new Error(`Tool not found: ${part.toolName}`);
            }

            const toolResult = await tool.execute(part.input as { [x: string]: any });

            const finalResult = await streamText({
              model: openai(model || 'gpt-4o'),
              messages: [...messages, part, { type: 'tool-result', toolName: part.toolName, result: toolResult }],
            });

            for await (const finalPart of finalResult.fullStream) {
              if (finalPart.type === 'text-delta') {
                sendJSON({ type: 'text_chunk', content: finalPart.text });
              }
            }
          } else if (part.type === 'text-delta') {
            sendJSON({ type: 'text_chunk', content: part.text });
          }
        }
      } catch (e) {
        console.error(e);
        const errorPayload = { type: 'error', message: (e as Error).message };
        sendJSON(errorPayload);
      } finally {
        sendJSON({ type: 'end' });
        controller.close();
      }
    },
  });

  return new Response(stream, {
    headers: { 'Content-Type': 'text/event-stream', 'Cache-Control': 'no-cache' },
  });
}

export default function Index() {
  const { models } = useLoaderData<typeof loader>();
  const [messages, setMessages] = useState<any[]>([]);
  const [input, setInput] = useState("");
  const [model, setModel] = useState(models[0]?.id || "gpt-4o");
  const [isLoading, setIsLoading] = useState(false);
  const [toolStatus, setToolStatus] = useState<any>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, toolStatus]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!input.trim()) return;

    const newMessages = [...messages, { role: 'user', content: input }];
    setMessages(newMessages);
    setInput("");
    setIsLoading(true);
    setToolStatus(null);

    let assistantMessage = { role: 'assistant', content: '' };
    let messageIndex = newMessages.length;

    try {
      const response = await fetch(e.currentTarget.action, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ messages: newMessages, model }),
      });

      if (!response.body) return;

      const reader = response.body.pipeThrough(new TextDecoderStream()).getReader();

      while (true) {
        const { value, done } = await reader.read();
        if (done) break;

        const lines = value.split('\n\n').filter(Boolean);
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = JSON.parse(line.substring(5));
            if (data.type === 'tool_call') {
              setToolStatus({ name: data.name, args: data.args });
            } else if (data.type === 'text_chunk') {
              if (toolStatus) setToolStatus(null);
              assistantMessage.content += data.content;
              setMessages(prev => {
                const updatedMessages = [...prev];
                if (updatedMessages[messageIndex]?.role === 'assistant') {
                  updatedMessages[messageIndex] = assistantMessage;
                } else {
                  updatedMessages.push(assistantMessage);
                }
                return updatedMessages;
              });
            } else if (data.type === 'end') {
              setIsLoading(false);
              return;
            } else if (data.type === 'error') {
              console.error('Error from server:', data.message);
              setIsLoading(false);
              setToolStatus({ name: 'Error', args: { message: data.message } });
              return;
            }
          }
        }
      }
    } catch (error) {
      console.error("Fetch error:", error);
      setIsLoading(false);
      setToolStatus({ name: 'Error', args: { message: 'Failed to connect to the server.' } });
    }
  };

  return (
    <div style={{ fontFamily: "system-ui, sans-serif", lineHeight: "1.8", padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      <h1>Chat Bot</h1>
      <div style={{ border: "1px solid #ccc", borderRadius: "8px", height: "500px", overflowY: "auto", padding: "10px", display: "flex", flexDirection: "column" }}>
        {messages.map((m, i) => (
          <div key={i} style={{ alignSelf: m.role === 'user' ? 'flex-end' : 'flex-start', background: m.role === 'user' ? '#dcf8c6' : '#f1f0f0', borderRadius: '10px', padding: '8px 12px', margin: '5px', maxWidth: '70%' }}>
            <strong>{m.role === 'user' ? 'You' : 'Assistant'}:</strong> <pre style={{ whiteSpace: 'pre-wrap', margin: 0, fontFamily: 'inherit' }}>{m.content}</pre>
          </div>
        ))}
        {toolStatus && (
          <div style={{ alignSelf: 'flex-start', background: '#e0e0e0', borderRadius: '10px', padding: '8px 12px', margin: '5px', maxWidth: '70%' }}>
            <i>Calling tool: {toolStatus.name}({JSON.stringify(toolStatus.args)})</i>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>
      <Form method="post" onSubmit={handleSubmit} style={{ display: "flex", marginTop: "10px" }}>
        <input
          type="text"
          name="message"
          value={input}
          onChange={(e) => setInput(e.target.value)}
          disabled={isLoading}
          style={{ flexGrow: 1, padding: "8px", borderRadius: "5px 0 0 5px", border: "1px solid #ccc" }}
        />
        <select
          name="model"
          value={model}
          onChange={(e) => setModel(e.target.value)}
          disabled={isLoading}
          style={{ padding: "8px", border: "1px solid #ccc", borderLeft: "none" }}
        >
          {models.map((m: any) => (
            <option key={m.id} value={m.id} title={m.description}>
              {m.name}
            </option>
          ))}
        </select>
        <button type="submit" disabled={isLoading} style={{ padding: "8px 15px", borderRadius: "0 5px 5px 0", border: "1px solid #ccc", borderLeft: "none", cursor: "pointer" }}>
          {isLoading ? '...' : 'Send'}
        </button>
      </Form>
    </div>
  );
}
